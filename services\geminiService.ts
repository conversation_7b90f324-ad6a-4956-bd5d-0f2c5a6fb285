import { GoogleGenAI, Type } from "@google/genai";

const PROMPT_OPTIMIZER_MODEL = 'gemini-2.5-flash';
const IMAGE_GENERATION_MODEL = 'imagen-4.0-generate-001';

const OPTIMIZER_SYSTEM_INSTRUCTION = `You are an expert visual analyst and prompt engineer for generative AI art models. Your task is to analyze an uploaded image and a desired artistic style, then describe the image in a detailed, descriptive, and artistically rich scene. Populate the provided JSON schema with creative and complementary details to generate a high-quality image. Focus on visual richness and coherence, ensuring the output aligns with the requested style.`;

const responseSchema = {
    type: Type.OBJECT,
    properties: {
        subject: { type: Type.STRING, description: "The main subject of the image, e.g., 'a majestic lion', 'an ancient robot'." },
        setting: { type: Type.STRING, description: "The background or environment, e.g., 'on a sun-drenched savanna', 'in a post-apocalyptic city'." },
        action: { type: Type.STRING, description: "What the subject is doing, e.g., 'roaring at the sky', 'gazing at the ruins'." },
        style: { type: Type.STRING, description: "The artistic style, e.g., 'photorealistic', 'digital painting', 'impressionistic watercolor'." },
        lighting: { type: Type.STRING, description: "The lighting conditions, e.g., 'cinematic lighting', 'golden hour', 'neon glow'." },
        composition: { type: Type.STRING, description: "The camera shot or composition, e.g., 'wide-angle shot', 'dynamic low-angle shot', 'close-up portrait'." },
        details: { type: Type.STRING, description: "Additional details and keywords to enhance the image, e.g., 'intricate details, hyperrealistic, masterpiece, 8k'." },
    },
    required: ["subject", "setting", "style", "details", "action", "lighting", "composition"]
};


export async function generatePromptFromImage(
  imageData: string, 
  style: string, 
  apiKey: string, 
  creativity: number,
  negativePrompt: string
): Promise<string> {
  if (!apiKey) {
    throw new Error("API key is missing.");
  }
  const ai = new GoogleGenAI({ apiKey });

  const match = imageData.match(/^data:(image\/\w+);base64,(.*)$/);
  if (!match) {
      throw new Error("Invalid image data format.");
  }
  const [, mimeType, base64Data] = match;

  const imagePart = {
      inlineData: {
          mimeType,
          data: base64Data,
      },
  };
  
  let analysisText = `Analyze this image in extreme detail. Based on your analysis, generate a rich, descriptive prompt by filling out all fields in the JSON schema. Describe the 'subject', 'setting', 'action', the overall 'style' (ensuring it is consistent with "${style}"), the 'lighting', the camera 'composition', and any other important visual 'details'.`;
  if (negativePrompt && negativePrompt.trim() !== '') {
    analysisText += ` Crucially, avoid including the following concepts or elements in your description: "${negativePrompt}".`;
  }
  const textPart = {
      text: analysisText,
  };

  try {
    const response = await ai.models.generateContent({
      model: PROMPT_OPTIMIZER_MODEL,
      contents: { parts: [imagePart, textPart] },
      config: {
        systemInstruction: OPTIMIZER_SYSTEM_INSTRUCTION,
        temperature: creativity,
        topP: 0.95,
        responseMimeType: "application/json",
        responseSchema: responseSchema,
      }
    });
    
    const jsonText = response.text.trim();
    if (!jsonText) {
        throw new Error("The model returned an empty response.");
    }

    const sceneData = JSON.parse(jsonText);
    
    const getLightingDefault = (selectedStyle: string): string => {
        switch (selectedStyle) {
            case 'Cinematic':
                return 'dramatic cinematic lighting, high contrast';
            case 'Photorealistic':
                return 'natural, soft lighting, hyperrealistic';
            case 'Watercolor':
                return 'soft, diffused lighting, wet-on-wet technique';
            case 'Anime':
                return 'vibrant, cel-shaded lighting, dynamic shadows';
            case 'Digital Painting':
                return 'rich, painterly lighting, visible brush strokes';
            default:
                return 'appropriate lighting';
        }
    };
    
    const defaults = {
        subject: "A detailed scene",
        action: "a visually interesting scene",
        setting: "a vibrant setting",
        style: style,
        lighting: getLightingDefault(style),
        composition: "medium shot, balanced composition",
        details: "high quality, sharp focus"
    };

    const finalSceneData = {
        subject: sceneData.subject?.trim() || defaults.subject,
        setting: sceneData.setting?.trim() || defaults.setting,
        action: sceneData.action?.trim() || defaults.action,
        style: sceneData.style?.trim() || defaults.style,
        lighting: sceneData.lighting?.trim() || defaults.lighting,
        composition: sceneData.composition?.trim() || defaults.composition,
        details: sceneData.details?.trim() || defaults.details,
    };

    const promptParts = [
        finalSceneData.subject,
        finalSceneData.action,
        finalSceneData.setting,
        finalSceneData.style,
        finalSceneData.lighting,
        finalSceneData.composition,
        finalSceneData.details
    ];

    const optimizedPrompt = promptParts.filter(part => part && part.trim() !== '').join(', ');

    if (!optimizedPrompt) {
        throw new Error("The generated scene description was empty.");
    }

    return optimizedPrompt;
  } catch (error) {
    console.error("Error analyzing image:", error);
     if (error instanceof SyntaxError) {
        throw new Error("Failed to parse the structured response from the API. The model may have returned invalid JSON.");
    }
    throw new Error("Failed to communicate with the Gemini API for image analysis.");
  }
}

export async function generateImage(prompt: string, apiKey: string): Promise<string> {
  if (!apiKey) {
    throw new Error("API key is missing.");
  }
  const ai = new GoogleGenAI({ apiKey });

  try {
    const response = await ai.models.generateImages({
        model: IMAGE_GENERATION_MODEL,
        prompt: prompt,
        config: {
          numberOfImages: 1,
          outputMimeType: 'image/png',
          aspectRatio: '1:1',
        },
    });

    if (!response.generatedImages || response.generatedImages.length === 0) {
        throw new Error("The model did not return any images.");
    }

    const base64ImageBytes = response.generatedImages[0].image.imageBytes;
    return `data:image/png;base64,${base64ImageBytes}`;
  } catch (error) {
    console.error("Error generating image:", error);
    throw new Error("Failed to communicate with the Gemini API for image generation.");
  }
}