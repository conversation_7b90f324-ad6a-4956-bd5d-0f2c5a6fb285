
import React from 'react';

interface ImageDisplayProps {
  imageUrl: string | null;
  isLoading: boolean;
}

const ImageDisplay: React.FC<ImageDisplayProps> = ({ imageUrl, isLoading }) => {
  if (!imageUrl && !isLoading) {
    return null;
  }

  return (
    <div className="mt-8">
      <h2 className="text-xl font-semibold text-[#1974e8] mb-4 text-center">Generated Image</h2>
      <div className="aspect-square w-full max-w-xl mx-auto bg-slate-50 rounded-2xl border border-slate-200 flex items-center justify-center overflow-hidden">
        {isLoading && (
          <div className="w-full h-full bg-slate-100 animate-pulse flex items-center justify-center">
             <svg className="w-12 h-12 text-slate-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
             </svg>
          </div>
        )}
        {!isLoading && imageUrl && (
          <img
            src={imageUrl}
            alt="Generated by Gemini"
            className="w-full h-full object-cover transition-opacity duration-500 opacity-0"
            onLoad={(e) => (e.currentTarget.style.opacity = '1')}
          />
        )}
      </div>
    </div>
  );
};

export default ImageDisplay;
